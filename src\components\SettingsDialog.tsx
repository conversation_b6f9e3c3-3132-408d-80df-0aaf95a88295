import React, { useState, useEffect } from 'react';
import { X, Setting<PERSON>, Eye, EyeOff, TestTube, Check, AlertCircle, RefreshCw } from 'lucide-react';
import { storageService } from '../services/storageService';
import { aiServiceManager } from '../services/aiProviders/aiServiceManager';
import { AI_PROVIDERS, AIProviderType } from '../services/aiProviders/types';

interface SettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

// 删除原有的API_PROVIDERS定义，使用新的类型系统

export const SettingsDialog: React.FC<SettingsDialogProps> = ({ isOpen, onClose }) => {
  const [settings, setSettings] = useState({
    apiProvider: AIProviderType.OPENAI,
    apiKey: '',
    apiBaseURL: 'https://api.openai.com/v1',
    model: 'gpt-3.5-turbo',
    customModels: '',
    temperature: 0.7,
    maxTokens: 500,
    autoSave: true,
    autoSaveInterval: 30,
  });
  
  const [showApiKey, setShowApiKey] = useState(false);
  const [isTestingAPI, setIsTestingAPI] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // 加载设置
  useEffect(() => {
    if (isOpen) {
      const savedSettings = storageService.getSettings();
      setSettings(prev => ({
        ...prev,
        ...savedSettings,
        apiKey: savedSettings.aiApiKey || '',
      }));
    }
  }, [isOpen]);

  // 处理提供商变更
  const handleProviderChange = (providerId: AIProviderType) => {
    const provider = AI_PROVIDERS[providerId];
    if (provider) {
      setSettings(prev => ({
        ...prev,
        apiProvider: providerId,
        apiBaseURL: provider.baseURL,
        model: provider.models[0] || '',
      }));
    }
  };

  // 测试API连接
  const testAPIConnection = async () => {
    setIsTestingAPI(true);
    setTestResult(null);

    try {
      // 备份当前配置
      const originalConfig = aiServiceManager.getConfig();

      // 临时设置API配置进行测试
      aiServiceManager.setProvider(settings.apiProvider);
      aiServiceManager.updateConfig({
        apiKey: settings.apiKey,
        baseURL: settings.apiBaseURL,
        model: settings.model,
        temperature: settings.temperature,
        maxTokens: settings.maxTokens
      });

      // 使用专门的测试方法
      const result = await aiServiceManager.testConnection();
      setTestResult(result);

      // 恢复原始配置
      aiServiceManager.updateConfig(originalConfig);

    } catch (error) {
      setTestResult({
        success: false,
        message: `测试过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`
      });
    } finally {
      setIsTestingAPI(false);
    }
  };

  // 保存设置
  const handleSave = () => {
    const settingsToSave = {
      autoSave: settings.autoSave,
      autoSaveInterval: settings.autoSaveInterval,
      aiApiKey: settings.apiKey,
      aiProvider: settings.apiProvider,
      aiBaseURL: settings.apiBaseURL,
      aiModel: settings.model,
      aiTemperature: settings.temperature,
      aiMaxTokens: settings.maxTokens,
    };

    storageService.saveSettings(settingsToSave);

    // 更新AI服务配置
    aiServiceManager.setProvider(settings.apiProvider);
    aiServiceManager.updateConfig({
      apiKey: settings.apiKey,
      baseURL: settings.apiBaseURL,
      model: settings.model,
      temperature: settings.temperature,
      maxTokens: settings.maxTokens
    });

    onClose();
  };

  const currentProvider = AI_PROVIDERS[settings.apiProvider];
  const availableModels = settings.apiProvider === AIProviderType.CUSTOM
    ? settings.customModels.split(',').map(m => m.trim()).filter(Boolean)
    : currentProvider?.models || [];

  if (!isOpen) return null;

  return (
    <div className="dialog-overlay">
      <div className="dialog-content p-6 max-w-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gradient flex items-center">
            <Settings size={20} className="mr-2" />
            应用设置
          </h2>
          <button onClick={onClose} className="btn-ghost p-1">
            <X size={20} />
          </button>
        </div>

        <div className="space-y-6 max-h-96 overflow-y-auto">
          {/* AI API 设置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">AI API 配置</h3>
            
            {/* API 提供商选择 */}
            <div>
              <label className="block text-sm font-medium mb-2">API 提供商</label>
              <select
                value={settings.apiProvider}
                onChange={(e) => handleProviderChange(e.target.value as AIProviderType)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              >
                {Object.values(AI_PROVIDERS).map(provider => (
                  <option key={provider.id} value={provider.id}>
                    {provider.name}
                  </option>
                ))}
              </select>
            </div>

            {/* API 密钥 */}
            <div>
              <label className="block text-sm font-medium mb-2">API 密钥</label>
              <div className="relative">
                <input
                  type={showApiKey ? 'text' : 'password'}
                  value={settings.apiKey}
                  onChange={(e) => setSettings(prev => ({ ...prev, apiKey: e.target.value }))}
                  placeholder="请输入API密钥..."
                  className="w-full px-3 py-2 pr-20 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                  <button
                    type="button"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="p-1 text-gray-400 hover:text-gray-600"
                  >
                    {showApiKey ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                  <button
                    type="button"
                    onClick={testAPIConnection}
                    disabled={isTestingAPI || !settings.apiKey.trim()}
                    className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50"
                    title="测试连接"
                  >
                    <TestTube size={16} />
                  </button>
                </div>
              </div>
              {testResult && (
                <div className={`mt-2 text-sm flex items-center ${
                  testResult.success ? 'text-green-600' : 'text-red-600'
                }`}>
                  {testResult.success ? <Check size={16} /> : <AlertCircle size={16} />}
                  <span className="ml-1">{testResult.message}</span>
                </div>
              )}
            </div>

            {/* API 基础URL */}
            <div>
              <label className="block text-sm font-medium mb-2">API 基础URL</label>
              <input
                type="text"
                value={settings.apiBaseURL}
                onChange={(e) => setSettings(prev => ({ ...prev, apiBaseURL: e.target.value }))}
                placeholder="https://api.openai.com/v1"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              />
            </div>

            {/* 模型选择 */}
            <div>
              <label className="block text-sm font-medium mb-2">模型</label>
              {settings.apiProvider === AIProviderType.CUSTOM ? (
                <div className="space-y-2">
                  <input
                    type="text"
                    value={settings.customModels}
                    onChange={(e) => setSettings(prev => ({ ...prev, customModels: e.target.value }))}
                    placeholder="输入模型名称，用逗号分隔"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                  />
                  <select
                    value={settings.model}
                    onChange={(e) => setSettings(prev => ({ ...prev, model: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                  >
                    {availableModels.map(model => (
                      <option key={model} value={model}>{model}</option>
                    ))}
                  </select>
                </div>
              ) : (
                <select
                  value={settings.model}
                  onChange={(e) => setSettings(prev => ({ ...prev, model: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                >
                  {availableModels.map(model => (
                    <option key={model} value={model}>{model}</option>
                  ))}
                </select>
              )}
            </div>

            {/* 高级参数 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  温度 ({settings.temperature})
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={settings.temperature}
                  onChange={(e) => setSettings(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">最大令牌数</label>
                <input
                  type="number"
                  min="100"
                  max="4000"
                  value={settings.maxTokens}
                  onChange={(e) => setSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* 自动保存设置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">自动保存</h3>
            
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="autoSave"
                checked={settings.autoSave}
                onChange={(e) => setSettings(prev => ({ ...prev, autoSave: e.target.checked }))}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
              <label htmlFor="autoSave" className="text-sm font-medium">
                启用自动保存
              </label>
            </div>

            {settings.autoSave && (
              <div>
                <label className="block text-sm font-medium mb-2">
                  自动保存间隔 ({settings.autoSaveInterval}秒)
                </label>
                <input
                  type="range"
                  min="10"
                  max="300"
                  step="10"
                  value={settings.autoSaveInterval}
                  onChange={(e) => setSettings(prev => ({ ...prev, autoSaveInterval: parseInt(e.target.value) }))}
                  className="w-full"
                />
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
          <button onClick={onClose} className="btn-ghost">
            取消
          </button>
          <button onClick={handleSave} className="btn-primary">
            保存设置
          </button>
        </div>
      </div>
    </div>
  );
};
