import {
  AIProvider,
  AIGenerationRequest,
  AIGenerationResponse,
  AITestConnectionResult,
  AIProviderConfig,
  AIFetchModelsResult,
  AIModelInfo
} from './types';

// OpenAI API请求格式
interface OpenAIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface OpenAIRequest {
  model: string;
  messages: OpenAIMessage[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenAIErrorResponse {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

// OpenAI模型列表API响应格式
interface OpenAIModelsResponse {
  object: string;
  data: Array<{
    id: string;
    object: string;
    created: number;
    owned_by: string;
  }>;
}

// 请求超时配置
const REQUEST_TIMEOUT = 10000; // 10秒
const MAX_RETRIES = 3;

export class OpenAIProvider extends AIProvider {
  constructor(config: AIProviderConfig) {
    super(config);
  }

  getProviderName(): string {
    return 'OpenAI';
  }

  getSupportedModels(): string[] {
    return ['gpt-4', 'gpt-4-turbo', 'gpt-4o', 'gpt-3.5-turbo', 'gpt-4o-mini'];
  }

  async fetchAvailableModels(): Promise<AIFetchModelsResult> {
    if (!this.config.apiKey.trim()) {
      return {
        success: false,
        models: [],
        message: '请先配置OpenAI API密钥'
      };
    }

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

        const response = await fetch(`${this.config.baseURL}/models`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData: OpenAIErrorResponse = await response.json();
          throw new Error(`API请求失败 (${response.status}): ${errorData.error?.message || response.statusText}`);
        }

        const data: OpenAIModelsResponse = await response.json();

        if (!data.data || !Array.isArray(data.data)) {
          throw new Error('API返回格式异常：缺少models数据');
        }

        // 过滤并转换模型数据，只保留聊天模型
        const models: AIModelInfo[] = data.data
          .filter(model =>
            model.id.includes('gpt') ||
            model.id.includes('chat') ||
            model.id.includes('turbo')
          )
          .map(model => ({
            id: model.id,
            name: model.id,
            created: model.created
          }))
          .sort((a, b) => a.id.localeCompare(b.id));

        return {
          success: true,
          models,
          message: `成功获取 ${models.length} 个可用模型`
        };

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (error instanceof Error && error.name === 'AbortError') {
          lastError = new Error('请求超时，请检查网络连接');
        }

        // 如果不是最后一次尝试，等待后重试
        if (attempt < MAX_RETRIES) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          continue;
        }
      }
    }

    return {
      success: false,
      models: [],
      message: `获取模型列表失败 (重试${MAX_RETRIES}次): ${lastError?.message || '未知错误'}`
    };
  }

  async testConnection(): Promise<AITestConnectionResult> {
    const startTime = Date.now();
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

        const response = await fetch(`${this.config.baseURL}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.config.apiKey}`,
          },
          body: JSON.stringify({
            model: this.config.model,
            messages: [
              {
                role: 'user',
                content: '测试连接，请回复"连接成功"'
              }
            ],
            max_tokens: 10,
            temperature: 0
          } as OpenAIRequest),
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        const latency = Date.now() - startTime;

        if (!response.ok) {
          const errorData: OpenAIErrorResponse = await response.json();
          return {
            success: false,
            message: `API请求失败 (${response.status}): ${errorData.error?.message || response.statusText}`,
            latency
          };
        }

        const data: OpenAIResponse = await response.json();

        if (data.choices && data.choices.length > 0) {
          return {
            success: true,
            message: 'OpenAI API连接测试成功！',
            latency
          };
        } else {
          return {
            success: false,
            message: 'API返回格式异常：缺少choices字段',
            latency
          };
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (error instanceof Error && error.name === 'AbortError') {
          return {
            success: false,
            message: '请求超时，请检查网络连接',
            latency: Date.now() - startTime
          };
        }

        if (error instanceof TypeError && error.message.includes('fetch')) {
          return {
            success: false,
            message: '网络连接失败，请检查URL是否正确',
            latency: Date.now() - startTime
          };
        }

        // 如果不是最后一次尝试，等待后重试
        if (attempt < MAX_RETRIES) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          continue;
        }
      }
    }

    return {
      success: false,
      message: `连接失败 (重试${MAX_RETRIES}次): ${lastError?.message || '未知错误'}`,
      latency: Date.now() - startTime
    };
  }

  async generateResponse(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    if (!this.config.apiKey.trim()) {
      throw new Error('请先配置OpenAI API密钥');
    }

    const openaiRequest: OpenAIRequest = {
      model: request.model || this.config.model,
      messages: request.messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: request.temperature ?? this.config.temperature,
      max_tokens: request.maxTokens ?? this.config.maxTokens
    };

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify(openaiRequest)
      });

      if (!response.ok) {
        const errorData: OpenAIErrorResponse = await response.json();
        throw new Error(`OpenAI API错误 (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const data: OpenAIResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('OpenAI API返回格式异常：缺少choices字段');
      }

      const choice = data.choices[0];
      
      return {
        content: choice.message.content || '',
        usage: {
          inputTokens: data.usage.prompt_tokens,
          outputTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens
        },
        finishReason: choice.finish_reason
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`OpenAI API调用失败: ${String(error)}`);
    }
  }
}
