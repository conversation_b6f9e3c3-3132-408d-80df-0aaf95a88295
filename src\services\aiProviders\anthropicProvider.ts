import {
  AIProvider,
  AIGenerationRequest,
  AIGenerationResponse,
  AITestConnectionResult,
  AIProviderConfig,
  AIFetchModelsResult,
  AIModelInfo
} from './types';

// Anthropic API请求格式
interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface AnthropicRequest {
  model: string;
  messages: AnthropicMessage[];
  max_tokens: number;
  temperature?: number;
  system?: string;
}

interface AnthropicResponse {
  id: string;
  type: string;
  role: string;
  content: Array<{
    type: string;
    text: string;
  }>;
  model: string;
  stop_reason: string;
  stop_sequence: string | null;
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
}

interface AnthropicErrorResponse {
  type: string;
  error: {
    type: string;
    message: string;
  };
}

// 请求超时配置
const REQUEST_TIMEOUT = 10000; // 10秒
const MAX_RETRIES = 3;

export class AnthropicProvider extends AIProvider {
  constructor(config: AIProviderConfig) {
    super(config);
  }

  getProviderName(): string {
    return 'Anthropic Claude';
  }

  getSupportedModels(): string[] {
    return [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307',
      'claude-3-5-sonnet-20241022',
      'claude-3-5-haiku-20241022'
    ];
  }

  async fetchAvailableModels(): Promise<AIFetchModelsResult> {
    // Anthropic目前没有公开的模型列表API，返回静态列表
    // 但保持接口一致性，以便将来API可用时可以轻松更新

    if (!this.config.apiKey.trim()) {
      return {
        success: false,
        models: [],
        message: '请先配置Anthropic API密钥'
      };
    }

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      const staticModels: AIModelInfo[] = [
        {
          id: 'claude-3-opus-20240229',
          name: 'Claude 3 Opus',
          description: 'Anthropic最强大的模型，适合复杂任务',
          contextLength: 200000
        },
        {
          id: 'claude-3-sonnet-20240229',
          name: 'Claude 3 Sonnet',
          description: '平衡性能和成本的模型',
          contextLength: 200000
        },
        {
          id: 'claude-3-haiku-20240307',
          name: 'Claude 3 Haiku',
          description: '快速响应的轻量级模型',
          contextLength: 200000
        },
        {
          id: 'claude-3-5-sonnet-20241022',
          name: 'Claude 3.5 Sonnet',
          description: '最新的Claude 3.5 Sonnet模型',
          contextLength: 200000
        },
        {
          id: 'claude-3-5-haiku-20241022',
          name: 'Claude 3.5 Haiku',
          description: '最新的Claude 3.5 Haiku模型',
          contextLength: 200000
        }
      ];

      return {
        success: true,
        models: staticModels,
        message: `获取到 ${staticModels.length} 个Anthropic模型（静态列表）`,
        cached: true
      };

    } catch (error) {
      return {
        success: false,
        models: [],
        message: `获取模型列表失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  async testConnection(): Promise<AITestConnectionResult> {
    const startTime = Date.now();
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

        const response = await fetch(`${this.config.baseURL}/messages`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.config.apiKey,
            'anthropic-version': '2023-06-01',
          },
          body: JSON.stringify({
            model: this.config.model,
            messages: [
              {
                role: 'user',
                content: '测试连接，请回复"连接成功"'
              }
            ],
            max_tokens: 10,
            temperature: 0
          } as AnthropicRequest),
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        const latency = Date.now() - startTime;

        if (!response.ok) {
          const errorData: AnthropicErrorResponse = await response.json();
          return {
            success: false,
            message: `API请求失败 (${response.status}): ${errorData.error?.message || response.statusText}`,
            latency
          };
        }

        const data: AnthropicResponse = await response.json();

        if (data.content && data.content.length > 0) {
          return {
            success: true,
            message: 'Anthropic Claude API连接测试成功！',
            latency
          };
        } else {
          return {
            success: false,
            message: 'API返回格式异常：缺少content字段',
            latency
          };
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (error instanceof Error && error.name === 'AbortError') {
          return {
            success: false,
            message: '请求超时，请检查网络连接',
            latency: Date.now() - startTime
          };
        }

        if (error instanceof TypeError && error.message.includes('fetch')) {
          return {
            success: false,
            message: '网络连接失败，请检查URL是否正确',
            latency: Date.now() - startTime
          };
        }

        // 如果不是最后一次尝试，等待后重试
        if (attempt < MAX_RETRIES) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          continue;
        }
      }
    }

    return {
      success: false,
      message: `连接失败 (重试${MAX_RETRIES}次): ${lastError?.message || '未知错误'}`,
      latency: Date.now() - startTime
    };
  }

  async generateResponse(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    if (!this.config.apiKey.trim()) {
      throw new Error('请先配置Anthropic API密钥');
    }

    // 分离系统消息和用户/助手消息
    let systemMessage = '';
    const messages: AnthropicMessage[] = [];
    
    for (const msg of request.messages) {
      if (msg.role === 'system') {
        systemMessage = msg.content;
      } else {
        messages.push({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        });
      }
    }

    const anthropicRequest: AnthropicRequest = {
      model: request.model || this.config.model,
      messages,
      max_tokens: request.maxTokens ?? this.config.maxTokens,
      temperature: request.temperature ?? this.config.temperature
    };

    // 如果有系统消息，添加到请求中
    if (systemMessage) {
      anthropicRequest.system = systemMessage;
    }

    try {
      const response = await fetch(`${this.config.baseURL}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.config.apiKey,
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify(anthropicRequest)
      });

      if (!response.ok) {
        const errorData: AnthropicErrorResponse = await response.json();
        throw new Error(`Anthropic API错误 (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const data: AnthropicResponse = await response.json();
      
      if (!data.content || data.content.length === 0) {
        throw new Error('Anthropic API返回格式异常：缺少content字段');
      }

      // 提取文本内容
      const textContent = data.content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join('');
      
      return {
        content: textContent,
        usage: {
          inputTokens: data.usage.input_tokens,
          outputTokens: data.usage.output_tokens,
          totalTokens: data.usage.input_tokens + data.usage.output_tokens
        },
        finishReason: data.stop_reason
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Anthropic API调用失败: ${String(error)}`);
    }
  }
}
